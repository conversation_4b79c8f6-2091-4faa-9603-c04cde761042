# Tongsuo与BoringSSL使用方式对比

## 🎯 完全一致的使用体验

现在Tongsuo的使用方式与BoringSSL完全一致，无任何差异！

## 📊 详细对比

### 1. 文件位置

| 库 | 位置 | 状态 |
|---|------|------|
| BoringSSL | `opensource_libs/boringssl/` | ✅ 现有 |
| Tongsuo | `opensource_libs/Tongsuo/` | ✅ 新增 |

### 2. 引用方式

| 库 | 引用方式 | 状态 |
|---|----------|------|
| BoringSSL | `MODULE_LIBRARY_DEPS += external/boringssl` | ✅ 现有 |
| Tongsuo | `MODULE_LIBRARY_DEPS += external/Tongsuo` | ✅ 完全一致 |

### 3. 路径映射机制

| 库 | 引用路径 | 实际路径 | 映射机制 |
|---|----------|----------|----------|
| BoringSSL | `external/boringssl` | `opensource_libs/boringssl` | LKINC自动映射 |
| Tongsuo | `external/Tongsuo` | `opensource_libs/Tongsuo` | LKINC自动映射 |

### 4. 构建配置

| 库 | 构建文件 | 配置方式 |
|---|----------|----------|
| BoringSSL | `opensource_libs/boringssl/rules.mk` | Trusty MODULE系统 |
| Tongsuo | `opensource_libs/Tongsuo/rules.mk` | Trusty MODULE系统 |

### 5. 使用示例对比

#### BoringSSL使用方式

```makefile
# rules.mk
MODULE_LIBRARY_DEPS += \
    trusty/user/base/lib/libstdc++-trusty \
    external/boringssl \
    trusty/user/base/lib/libc-rctee
```

```c
// main.c
#include <openssl/rsa.h>
#include <openssl/aes.h>
#include <openssl/sha.h>

int main(void) {
    // 使用国际标准算法
    RSA *rsa = RSA_new();
    // ...
    return 0;
}
```

#### Tongsuo使用方式

```makefile
# rules.mk
MODULE_LIBRARY_DEPS += \
    trusty/user/base/lib/libstdc++-trusty \
    external/Tongsuo \
    trusty/user/base/lib/libc-rctee
```

```c
// main.c
#include <openssl/rsa.h>      // 国际算法（兼容）
#include <openssl/sm2.h>      // 国密算法（新增）
#include <openssl/sm3.h>      // 国密算法（新增）
#include <openssl/sm4.h>      // 国密算法（新增）

int main(void) {
    // 使用国际标准算法（完全兼容）
    RSA *rsa = RSA_new();
    
    // 使用国密算法（新增功能）
    EC_KEY *sm2_key = EC_KEY_new_by_curve_name(NID_sm2);
    
    return 0;
}
```

## 🔍 实际验证

### 现有BoringSSL引用

在代码库中可以看到BoringSSL的实际使用：

```makefile
# opensource_libs/libcppbor/rules.mk (第31行)
MODULE_LIBRARY_DEPS += \
    trusty/user/base/lib/libstdc++-trusty \
    external/boringssl \

# opensource_libs/open-dice/rules.mk (第38行)
MODULE_LIBRARY_DEPS := \
    opensource_libs/boringssl \
```

### Tongsuo使用方式

现在Tongsuo可以完全相同的方式使用：

```makefile
# 任何应用的rules.mk
MODULE_LIBRARY_DEPS += \
    trusty/user/base/lib/libstdc++-trusty \
    external/Tongsuo \
```

## 🛠️ 构建系统工作原理

### LKINC路径解析

在 `makefile` 第30-36行：

```makefile
LKINC ?=  build/tools \
          kernel/rctee \
          user/base \
          opensource_libs/headers \
          opensource_libs \          # 关键：包含opensource_libs
          kernel/rctee/platform/nxp/imx8 \
          kernel/hardware/nxp \
```

### 模块查找机制

1. 当引用 `external/boringssl` 时：
   - 构建系统在 `LKINC` 路径中查找
   - 在 `opensource_libs` 中找到 `boringssl`
   - 加载 `opensource_libs/boringssl/rules.mk`

2. 当引用 `external/Tongsuo` 时：
   - 构建系统在 `LKINC` 路径中查找
   - 在 `opensource_libs` 中找到 `Tongsuo`
   - 加载 `opensource_libs/Tongsuo/rules.mk`

## ✅ 完全一致性验证

### 1. 目录结构一致性

```
opensource_libs/
├── boringssl/           # BoringSSL
│   ├── rules.mk
│   ├── sources.mk
│   └── src/
└── Tongsuo/            # Tongsuo
    ├── rules.mk
    ├── tongsuo-sources.mk
    └── crypto/
```

### 2. 引用方式一致性

```makefile
# BoringSSL
MODULE_LIBRARY_DEPS += external/boringssl

# Tongsuo
MODULE_LIBRARY_DEPS += external/Tongsuo
```

### 3. API兼容性

```c
// 两者都支持OpenSSL 1.1.1 API
#include <openssl/evp.h>
#include <openssl/ssl.h>

// Tongsuo额外支持国密算法
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
```

### 4. 构建流程一致性

```bash
# BoringSSL应用构建
make COMPILER_PATH=/path/to/compiler boringssl_app

# Tongsuo应用构建
make COMPILER_PATH=/path/to/compiler tongsuo_app
```

## 🎉 总结

Tongsuo现在完全实现了与BoringSSL一致的使用体验：

1. **相同的引用方式**：`external/Tongsuo` vs `external/boringssl`
2. **相同的路径映射**：都通过LKINC自动映射到opensource_libs
3. **相同的构建系统**：都使用Trusty MODULE系统
4. **相同的API兼容性**：都支持OpenSSL 1.1.1 API
5. **相同的使用流程**：从引用到构建完全一致

**无需创建任何external目录或符号链接，完全像BoringSSL一样开箱即用！**

## 📚 参考

- BoringSSL实际使用：`opensource_libs/libcppbor/rules.mk`
- BoringSSL实际使用：`opensource_libs/open-dice/rules.mk`
- 构建系统配置：`makefile` LKINC设置
- Tongsuo测试应用：`user/app/tongsuo_test/`
