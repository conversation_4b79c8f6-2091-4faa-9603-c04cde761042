# Copyright (C) 2024 The Trusty Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Tongsuo integration for Trusty - Similar to BoringSSL integration

LOCAL_DIR := $(GET_LOCAL_DIR)
LOCAL_PATH := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TARGET_ARCH := $(ARCH)
TARGET_2ND_ARCH := $(ARCH)

# Reset local variables
LOCAL_CFLAGS :=
LOCAL_C_INCLUDES :=
LOCAL_SRC_FILES :=
LOCAL_SRC_FILES_$(TARGET_ARCH) :=
LOCAL_SRC_FILES_$(TARGET_2ND_ARCH) :=
LOCAL_CFLAGS_$(TARGET_ARCH) :=
LOCAL_CFLAGS_$(TARGET_2ND_ARCH) :=
LOCAL_ADDITIONAL_DEPENDENCIES :=

# Default configuration - enable core Chinese cryptographic algorithms
TONGSUO_ENABLE_SM2 ?= true
TONGSUO_ENABLE_SM3 ?= true
TONGSUO_ENABLE_SM4 ?= true
TONGSUO_ENABLE_NTLS ?= true

# Optional features - disabled by default for minimal build
TONGSUO_ENABLE_ZKP ?= false
TONGSUO_ENABLE_PAILLIER ?= false
TONGSUO_ENABLE_EC_ELGAMAL ?= false

# Include source file configuration (auto-generated if needed)
TONGSUO_SOURCES_MK := $(LOCAL_DIR)/tongsuo-sources.mk
MODULE_SRCDEPS += $(TONGSUO_SOURCES_MK)

# Auto-generate source configuration if missing (requires Python)
ifeq ($(wildcard $(TONGSUO_SOURCES_MK)),)
$(info Tongsuo: Generating source configuration...)
$(shell cd $(LOCAL_DIR) && python3 build/generate_sources.py 2>/dev/null || echo "# Fallback configuration" > tongsuo-sources.mk)
endif

# Include source file configuration
include $(TONGSUO_SOURCES_MK)

# Verify we have source files defined
ifeq ($(TONGSUO_CRYPTO_SRCS)$(TONGSUO_SSL_SRCS),)
$(error Tongsuo: No source files found. Please check that $(LOCAL_DIR) contains Tongsuo source code and try: cd $(LOCAL_DIR) && python3 build/generate_sources.py)
endif

# Trusty-specific compile flags
MODULE_CFLAGS += \
    -D__linux__ \
    -D__TRUSTY__ \
    -DOPENSSL_NO_STDIO \
    -DOPENSSL_NO_SOCK \
    -DOPENSSL_NO_DGRAM \
    -DOPENSSL_NO_UI_CONSOLE \
    -DOPENSSL_NO_DEPRECATED \
    -DOPENSSL_API_COMPAT=0x10101000L \
    -DOPENSSL_NO_DYNAMIC_ENGINE \
    -DOPENSSL_NO_AUTOLOAD_CONFIG \
    -DOPENSSL_NO_AUTOERRINIT

# Enable Chinese National Cryptographic Algorithms
MODULE_CFLAGS += \
    -DOPENSSL_ENABLE_SM2 \
    -DOPENSSL_ENABLE_SM3 \
    -DOPENSSL_ENABLE_SM4 \
    -DOPENSSL_ENABLE_NTLS

# Disable features not suitable for Trusty
MODULE_CFLAGS += \
    -DOPENSSL_NO_APPS \
    -DOPENSSL_NO_TESTS \
    -DOPENSSL_NO_FUZZ_LIBFUZZER \
    -DOPENSSL_NO_FUZZ_AFL \
    -DOPENSSL_NO_UNIT_TEST

# Architecture-specific configurations
ifeq ($(ARCH),arm64)
MODULE_CFLAGS += \
    -DOPENSSL_AARCH64 \
    -DOPENSSL_STATIC_ARMCAP_NEON \
    -DOPENSSL_STATIC_ARMCAP_AES \
    -DOPENSSL_STATIC_ARMCAP_SHA1 \
    -DOPENSSL_STATIC_ARMCAP_SHA256
endif

ifeq ($(ARCH),arm)
MODULE_CFLAGS += \
    -DOPENSSL_ARM \
    -DOPENSSL_STATIC_ARMCAP
endif

# Memory and threading configuration for Trusty
MODULE_CFLAGS += \
    -DOPENSSL_NO_SECURE_MEMORY \
    -DOPENSSL_THREADS \
    -D_REENTRANT

# Optimization flags
MODULE_CFLAGS += \
    -DOPENSSL_SMALL \
    -DOPENSSL_NO_HEARTBEATS \
    -DOPENSSL_NO_COMP

# Source files from tongsuo-sources.mk
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_CRYPTO_SRCS))
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_SSL_SRCS))

# Include directories
MODULE_INCLUDES += \
    $(LOCAL_DIR)/crypto \
    $(LOCAL_DIR)/ssl \
    $(LOCAL_DIR)/include/internal

# Export include directories
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# Auto-detect and enable optional features based on available sources
ifneq ($(TONGSUO_ZKP_SRCS),)
ifeq ($(TONGSUO_ENABLE_ZKP),true)
MODULE_CFLAGS += -DOPENSSL_ENABLE_ZKP
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_ZKP_SRCS))
endif
endif

ifneq ($(TONGSUO_PAILLIER_SRCS),)
ifeq ($(TONGSUO_ENABLE_PAILLIER),true)
MODULE_CFLAGS += -DOPENSSL_ENABLE_PAILLIER
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_PAILLIER_SRCS))
endif
endif

ifneq ($(TONGSUO_EC_ELGAMAL_SRCS),)
ifeq ($(TONGSUO_ENABLE_EC_ELGAMAL),true)
MODULE_CFLAGS += -DOPENSSL_ENABLE_EC_ELGAMAL
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_EC_ELGAMAL_SRCS))
endif
endif

# Assembly source files (architecture-specific)
ifeq ($(ARCH),arm64)
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_ASM_SRCS_ARM64))
endif

ifeq ($(ARCH),arm)
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_ASM_SRCS_ARM))
endif

ifeq ($(ARCH),x86_64)
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_ASM_SRCS_X86_64))
endif

# Filter out incompatible source files for Trusty
MODULE_SRCS := $(filter-out %/o_dir.c,$(MODULE_SRCS))
MODULE_SRCS := $(filter-out %/o_fopen.c,$(MODULE_SRCS))
MODULE_SRCS := $(filter-out %/ui_openssl.c,$(MODULE_SRCS))
MODULE_SRCS := $(filter-out %/apps/%,$(MODULE_SRCS))
MODULE_SRCS := $(filter-out %/test/%,$(MODULE_SRCS))
MODULE_SRCS := $(filter-out %/fuzz/%,$(MODULE_SRCS))

# Additional assembly flags
MODULE_ASMFLAGS += $(MODULE_CFLAGS)

# Link with required Trusty libraries
MODULE_LIBRARY_DEPS += \
    user/base/lib/libc-rctee

# Build information
$(info Tongsuo: Building with SM2=$(TONGSUO_ENABLE_SM2) SM3=$(TONGSUO_ENABLE_SM3) SM4=$(TONGSUO_ENABLE_SM4) NTLS=$(TONGSUO_ENABLE_NTLS))
ifneq ($(filter true,$(TONGSUO_ENABLE_ZKP) $(TONGSUO_ENABLE_PAILLIER) $(TONGSUO_ENABLE_EC_ELGAMAL)),)
$(info Tongsuo: Advanced features - ZKP=$(TONGSUO_ENABLE_ZKP) Paillier=$(TONGSUO_ENABLE_PAILLIER) EC-ElGamal=$(TONGSUO_ENABLE_EC_ELGAMAL))
endif

# Include the Trusty library build system
include make/rctee_lib.mk
